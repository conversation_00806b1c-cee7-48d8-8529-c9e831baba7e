/*
 * Copyright 2002-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.test.context.event;

import org.springframework.test.context.TestContext;

/**
 * {@link TestContextEvent} published by the {@link EventPublishingTestExecutionListener} when
 * {@link org.springframework.test.context.TestExecutionListener#afterTestMethod(TestContext)}
 * is invoked.
 *
 * <AUTHOR>
 * @since 5.2
 * @see org.springframework.test.context.event.annotation.AfterTestMethod @AfterTestMethod
 */
@SuppressWarnings("serial")
public class AfterTestMethodEvent extends TestContextEvent {

	public AfterTestMethodEvent(TestContext source) {
		super(source);
	}

}
