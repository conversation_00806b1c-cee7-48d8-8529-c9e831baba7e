<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

	<bean id="pet" class="org.springframework.beans.testfixture.beans.Pet">
		<constructor-arg value="Fido" />
	</bean>

	<beans profile="dev">
		<bean id="employee" class="org.springframework.beans.testfixture.beans.Employee">
			<property name="name" value="<PERSON>" />
			<property name="age" value="42" />
			<property name="company" value="Acme Widgets, Inc." />
		</bean>
	</beans>

</beans>
