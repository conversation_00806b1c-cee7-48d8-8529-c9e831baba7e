/*
 * Copyright 2002-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.test.context.configuration.interfaces;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @since 4.3
 */
@ExtendWith(SpringExtension.class)
class TestPropertySourceInterfaceTests implements TestPropertySourceTestInterface {

	@Autowired
	Environment env;


	@Test
	void propertiesAreAvailableInEnvironment() {
		assertThat(property("foo")).isEqualTo("bar");
		assertThat(property("enigma")).isEqualTo("42");
	}

	private String property(String key) {
		return env.getProperty(key);
	}


	@Configuration
	static class Config {
		/* no user beans required for these tests */
	}

}
