/*
 * Copyright 2002-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.test.context.initializers.annotation;

import org.junit.jupiter.api.Test;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.test.context.initializers.DevProfileInitializer;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests that verify support for {@link ApplicationContextInitializer
 * ApplicationContextInitializers} in conjunction with annotation-driven
 * configuration in the TestContext framework.
 *
 * <AUTHOR>
 * @since 3.2
 */
@SpringJUnitConfig(initializers = DevProfileInitializer.class, inheritInitializers = false)
public class OverriddenInitializersAnnotationConfigTests extends SingleInitializerAnnotationConfigTests {

	@Test
	@Override
	public void activeBeans() {
		assertThat(foo).isEqualTo("foo");
		assertThat(bar).isNull();
		assertThat(baz).isEqualTo("dev profile config");
	}

}
