/*
 * Copyright 2002-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.test.context.customizers;

import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @since 6.1
 */
@CustomizeWithBar
class InheritedContextCustomizerRegistrationTests extends LocalContextCustomizerRegistrationTests {

	@Autowired
	@Qualifier("bar")
	String bar;


	@Override
	@Test
	void injectedBean() {
		assertThat(fruit).isEqualTo("apple, banana, cherry");
		assertThat(foo).isEqualTo("bar");
		assertThat(bar).isEqualTo("baz");
	}

}
