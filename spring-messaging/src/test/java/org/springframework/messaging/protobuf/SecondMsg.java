// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: sample.proto
// Protobuf Java Version: 4.27.0

package org.springframework.messaging.protobuf;

/**
 * Protobuf type {@code SecondMsg}
 */
public final class SecondMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:SecondMsg)
    SecondMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 27,
      /* patch= */ 0,
      /* suffix= */ "",
      SecondMsg.class.getName());
  }
  // Use SecondMsg.newBuilder() to construct.
  private SecondMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SecondMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return org.springframework.messaging.protobuf.OuterSample.internal_static_SecondMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return org.springframework.messaging.protobuf.OuterSample.internal_static_SecondMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            org.springframework.messaging.protobuf.SecondMsg.class, org.springframework.messaging.protobuf.SecondMsg.Builder.class);
  }

  private int bitField0_;
  public static final int BLAH_FIELD_NUMBER = 1;
  private int blah_ = 0;
  /**
   * <code>optional int32 blah = 1;</code>
   * @return Whether the blah field is set.
   */
  @java.lang.Override
  public boolean hasBlah() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 blah = 1;</code>
   * @return The blah.
   */
  @java.lang.Override
  public int getBlah() {
    return blah_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, blah_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, blah_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof org.springframework.messaging.protobuf.SecondMsg)) {
      return super.equals(obj);
    }
    org.springframework.messaging.protobuf.SecondMsg other = (org.springframework.messaging.protobuf.SecondMsg) obj;

    if (hasBlah() != other.hasBlah()) return false;
    if (hasBlah()) {
      if (getBlah()
          != other.getBlah()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBlah()) {
      hash = (37 * hash) + BLAH_FIELD_NUMBER;
      hash = (53 * hash) + getBlah();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static org.springframework.messaging.protobuf.SecondMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static org.springframework.messaging.protobuf.SecondMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static org.springframework.messaging.protobuf.SecondMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(org.springframework.messaging.protobuf.SecondMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code SecondMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:SecondMsg)
      org.springframework.messaging.protobuf.SecondMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.springframework.messaging.protobuf.OuterSample.internal_static_SecondMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.springframework.messaging.protobuf.OuterSample.internal_static_SecondMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.springframework.messaging.protobuf.SecondMsg.class, org.springframework.messaging.protobuf.SecondMsg.Builder.class);
    }

    // Construct using org.springframework.messaging.protobuf.SecondMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      blah_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return org.springframework.messaging.protobuf.OuterSample.internal_static_SecondMsg_descriptor;
    }

    @java.lang.Override
    public org.springframework.messaging.protobuf.SecondMsg getDefaultInstanceForType() {
      return org.springframework.messaging.protobuf.SecondMsg.getDefaultInstance();
    }

    @java.lang.Override
    public org.springframework.messaging.protobuf.SecondMsg build() {
      org.springframework.messaging.protobuf.SecondMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public org.springframework.messaging.protobuf.SecondMsg buildPartial() {
      org.springframework.messaging.protobuf.SecondMsg result = new org.springframework.messaging.protobuf.SecondMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(org.springframework.messaging.protobuf.SecondMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.blah_ = blah_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof org.springframework.messaging.protobuf.SecondMsg) {
        return mergeFrom((org.springframework.messaging.protobuf.SecondMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(org.springframework.messaging.protobuf.SecondMsg other) {
      if (other == org.springframework.messaging.protobuf.SecondMsg.getDefaultInstance()) return this;
      if (other.hasBlah()) {
        setBlah(other.getBlah());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              blah_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int blah_ ;
    /**
     * <code>optional int32 blah = 1;</code>
     * @return Whether the blah field is set.
     */
    @java.lang.Override
    public boolean hasBlah() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 blah = 1;</code>
     * @return The blah.
     */
    @java.lang.Override
    public int getBlah() {
      return blah_;
    }
    /**
     * <code>optional int32 blah = 1;</code>
     * @param value The blah to set.
     * @return This builder for chaining.
     */
    public Builder setBlah(int value) {

      blah_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 blah = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBlah() {
      bitField0_ = (bitField0_ & ~0x00000001);
      blah_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:SecondMsg)
  }

  // @@protoc_insertion_point(class_scope:SecondMsg)
  private static final org.springframework.messaging.protobuf.SecondMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new org.springframework.messaging.protobuf.SecondMsg();
  }

  public static org.springframework.messaging.protobuf.SecondMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SecondMsg>
      PARSER = new com.google.protobuf.AbstractParser<SecondMsg>() {
    @java.lang.Override
    public SecondMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SecondMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SecondMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public org.springframework.messaging.protobuf.SecondMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

