/**
 * Contains abstractions and implementation classes for establishing TCP connections via
 * {@link org.springframework.messaging.tcp.TcpOperations TcpOperations},
 * handling messages via
 * {@link org.springframework.messaging.tcp.TcpConnectionHandler TcpConnectionHandler},
 * as well as sending messages via
 * {@link org.springframework.messaging.tcp.TcpConnection TcpConnection}.
 */
@NonNullApi
@NonNullFields
package org.springframework.messaging.tcp;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
