<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:aop="http://www.springframework.org/schema/aop"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.0.xsd
				http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.0.xsd">

	<aop:config>
		<aop:aspect id="countAgeCalls" ref="countingAdvice">
			<aop:after-throwing pointcut="execution(int getAge(..))" method="myAfterThrowingAdvice" throwing="ex"/>
		</aop:aspect>
	</aop:config>

	<bean id="getNameCounter" class="org.springframework.aop.testfixture.advice.CountingBeforeAdvice"/>

	<bean id="getAgeCounter" class="org.springframework.aop.testfixture.advice.CountingBeforeAdvice"/>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

	<bean id="countingAdvice" class="org.springframework.aop.config.CountingAspectJAdvice"/>

</beans>
