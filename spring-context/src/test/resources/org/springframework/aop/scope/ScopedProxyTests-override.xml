<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:aop="http://www.springframework.org/schema/aop"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.5.xsd">

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean" scope="request">
		<aop:scoped-proxy proxy-target-class="false"/>
		<property name="age" value="99"/>
	</bean>

	<bean class="org.springframework.beans.factory.config.PropertyOverrideConfigurer">
		<property name="properties">
			<map>
				<entry key="testBean.sex" value="male"/>
			</map>
		</property>
	</bean>

</beans>
