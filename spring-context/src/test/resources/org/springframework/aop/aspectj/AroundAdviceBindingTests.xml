<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:aop="http://www.springframework.org/schema/aop"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.0.xsd
				http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.0.xsd">

	<aop:config>
		<aop:aspect id="beforeAdviceBindingTests" ref="testAspect">
			<aop:around method="oneIntArg" pointcut="execution(* setAge(int)) and args(age)"	/>
			<aop:around method="oneObjectArg" pointcut="execution(* getAge()) and target(bean)"/>
			<aop:around method="oneIntAndOneObject"
				pointcut="execution(* setAge(..)) and args(age) and this(bean)" arg-names="thisJoinPoint,age,bean"/>
			<aop:around method="justJoinPoint" pointcut="execution(* getAge())"/>
		</aop:aspect>
	</aop:config>

	<bean id="testAspect" class="org.springframework.aop.aspectj.AroundAdviceBindingTestAspect"/>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

</beans>
