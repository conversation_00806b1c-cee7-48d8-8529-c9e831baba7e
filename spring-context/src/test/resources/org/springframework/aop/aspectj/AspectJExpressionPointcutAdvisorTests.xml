<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN 2.0//EN" "https://www.springframework.org/dtd/spring-beans-2.0.dtd">

<beans>

	<bean id="proxyCreator" class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator"/>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

	<bean id="advisor" class="org.springframework.aop.aspectj.AspectJExpressionPointcutAdvisor">
		<property name="expression"
				value="execution(org.springframework.beans.testfixture.beans.ITestBean[] org.springframework.beans.testfixture.beans.ITestBean.*(..))"/>
		<property name="advice" ref="interceptor"/>
	</bean>

	<bean id="interceptor" class="org.springframework.aop.aspectj.CallCountingInterceptor"/>

</beans>
