<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.0.xsd
       http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.0.xsd">


	<aop:config>
		<aop:aspect id="afterThrowingAdviceBindingTests" ref="testAspect">
			<aop:after-throwing
			    method="noArgs"
			    pointcut="execution(* exceptional(..))"
			/>
			<aop:after-throwing
				method="oneThrowable"
				throwing="t"
				pointcut="execution(* exceptional(..))"
			/>
			<aop:after-throwing
				method="oneRuntimeException"
				throwing="ex"
				pointcut="execution(* exceptional(..))"
			/>		
			<aop:after-throwing
				method="noArgsOnThrowableMatch"
				throwing="java.lang.Throwable"
				pointcut="execution(* exceptional(..))"
			/>
			<aop:after-throwing
				method="noArgsOnRuntimeExceptionMatch"
				throwing="java.lang.RuntimeException"
				pointcut="execution(* exceptional(..))"
			/>		
	</aop:aspect>	
	</aop:config>

	<bean id="testAspect" class="org.springframework.aop.aspectj.AfterThrowingAdviceBindingTestAspect"/>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

</beans>