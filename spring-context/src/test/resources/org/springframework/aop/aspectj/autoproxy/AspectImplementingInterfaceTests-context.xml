<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.0.xsd
       http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.0.xsd">

	<aop:config>
		<aop:aspect ref="interfaceExtendingAspect">
			<aop:pointcut id="anyOperation"
				expression="execution(* *(..))"/>
			<aop:around pointcut-ref="anyOperation" method="increment"/>
		</aop:aspect>
	</aop:config>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean" />

	<bean id="interfaceExtendingAspect"
		class="org.springframework.aop.aspectj.autoproxy.InterfaceExtendingAspect"/>
</beans>