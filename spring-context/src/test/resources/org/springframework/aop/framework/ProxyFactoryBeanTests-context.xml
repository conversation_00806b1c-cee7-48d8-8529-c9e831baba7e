<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN 2.0//EN" "https://www.springframework.org/dtd/spring-beans-2.0.dtd">

<beans>

	<!-- Simple target -->
	<bean id="test" class="org.springframework.beans.testfixture.beans.TestBean">
		<property name="name"><value>custom</value></property>
		<property name="age"><value>666</value></property>
	</bean>

	<bean id="debugInterceptor" class="org.springframework.aop.testfixture.interceptor.NopInterceptor"/>

	<bean id="test1" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="interfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>
		<property name="target"><ref bean="test"/></property>
		<property name="interceptorNames"><value>debugInterceptor</value></property>
	</bean>

	<!--
		Check that invoker is automatically added to wrap target.
		Non pointcut bean name should be wrapped in invoker.
	-->
	<bean id="autoInvoker" class="org.springframework.aop.framework.ProxyFactoryBean">

		<!--
			Aspect interfaces don't need to be included here.
			They may, for example, be added by global interceptors.
		 -->
		<property name="interfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>

		<!--
			Note that "test" is a target. An InvokerInterceptor
			will be added automatically.
		-->
		<property name="interceptorNames"><value>global*,test</value></property>

	</bean>

	<bean id="prototype" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="interfaces">
			<value>
				org.springframework.beans.testfixture.beans.ITestBean
			</value>
		</property>
		<property name="singleton"><value>false</value></property>
		<property name="interceptorNames"><value>test</value></property>
	</bean>

	<bean id="test2" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces">
			<value>
				org.springframework.beans.testfixture.beans.ITestBean
			</value>
		</property>
		<property name="singleton"><value>false</value></property>
		<property name="targetName"><value>test</value></property>
	</bean>

	<bean id="test3" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="autodetectInterfaces"><value>true</value></property>
		<property name="target"><ref bean="test"/></property>
		<property name="interceptorNames"><value>debugInterceptor</value></property>
	</bean>

	<bean id="test4" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="autodetectInterfaces"><value>true</value></property>
		<property name="singleton"><value>false</value></property>
		<property name="targetName"><value>test</value></property>
	</bean>

	<bean id="testCircle1" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="autodetectInterfaces"><value>true</value></property>
		<property name="targetName"><value>testCircleTarget1</value></property>
	</bean>

	<bean id="testCircleTarget1" class="org.springframework.beans.testfixture.beans.TestBean">
		<property name="name"><value>custom</value></property>
		<property name="age"><value>666</value></property>
		<property name="spouse"><ref bean="testCircle2"/></property>
	</bean>

	<bean id="testCircle2" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="autodetectInterfaces"><value>true</value></property>
		<property name="targetName"><value>testCircleTarget2</value></property>
	</bean>

	<bean id="testCircleTarget2" class="org.springframework.beans.testfixture.beans.TestBean">
		<property name="name"><value>custom</value></property>
		<property name="age"><value>666</value></property>
		<property name="spouse"><ref bean="testCircle1"/></property>
	</bean>

	<bean id="pointcuts" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>
		<property name="interceptorNames"><value>pointcutForVoid</value></property>
		<property name="targetName"><value>test</value></property>
	</bean>

	<bean id="pointcutForVoid" class="org.springframework.aop.framework.ProxyFactoryBeanTests$PointcutForVoid"/>

	<!--
		Invalid test for global pointcuts.
		Must have target because there are no interceptors.
	-->
	<!--
	<bean id="noInterceptorNamesWithoutTarget"
			class="org.springframework.aop.framework.ProxyFactoryBean"
	>
		<property name="proxyInterfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>

	</bean>

	<bean id="noInterceptorNamesWithTarget"
			class="org.springframework.aop.framework.ProxyFactoryBean"
	>
		<property name="proxyInterfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>
		<property name="target"><ref bean="test"/></property>

	</bean>
	-->

	<bean id="validGlobals" scope="singleton" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces"><value>org.springframework.context.ApplicationListener</value></property>
		<property name="interceptorNames"><value>debugInterceptor,global*,target2</value></property>
	</bean>

	<!--
		Global debug interceptor
	-->
	<bean id="global_debug" class="org.springframework.aop.interceptor.DebugInterceptor"/>

	<!--
		Will add aspect interface to all beans exposing globals
	-->
	<bean id="global_aspectInterface" class="org.springframework.aop.framework.ProxyFactoryBeanTests$GlobalIntroductionAdvice"/>

	<bean id="prototypeLockMixinAdvisor" class="org.springframework.aop.testfixture.mixin.LockMixinAdvisor" scope="prototype"/>

	<bean id="prototypeTestBean" class="org.springframework.beans.testfixture.beans.TestBean" scope="prototype"/>

	<bean id="prototypeTestBeanProxy" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="interfaces"><value>org.springframework.beans.testfixture.beans.ITestBean</value></property>
		<property name="singleton"><value>false</value></property>
		<property name="interceptorNames">
			<list>
				<value>prototypeLockMixinAdvisor</value>
				<value>prototypeTestBean</value>
			</list>
		</property>
	</bean>

	<bean id="prototypeLockMixinInterceptor" class="org.springframework.aop.testfixture.mixin.LockMixin" scope="prototype"/>

	<bean id="prototypeTestBeanProxySingletonTarget" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="interfaces">
			<list>
				<value>org.springframework.beans.testfixture.beans.ITestBean</value>
				<value>org.springframework.aop.testfixture.mixin.Lockable</value>
			</list>
		</property>
		<property name="singleton"><value>false</value></property>
		<property name="target"><ref bean="prototypeTestBean"/></property>
		<property name="interceptorNames">
			<list>
				<value>prototypeLockMixinInterceptor</value>
			</list>
		</property>
	</bean>

</beans>
