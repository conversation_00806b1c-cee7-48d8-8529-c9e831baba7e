<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN 2.0//EN" "https://www.springframework.org/dtd/spring-beans-2.0.dtd">

<beans>

	<bean id="dao" class="org.springframework.beans.factory.xml.DummyDao" autowire="constructor"/>

	<bean id="boPrototype" autowire="constructor" class="org.springframework.beans.factory.xml.DummyBoImpl"
			scope="prototype"/>

	<bean id="prototypeTargetSource" class="org.springframework.aop.target.PrototypeTargetSource">
		<property name="targetBeanName"><value>boPrototype</value></property>
	</bean>

	<bean id="prototypeBenchmark" class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="targetSource"><ref bean="prototypeTargetSource"/></property>
		<property name="proxyInterfaces"><value>org.springframework.beans.factory.xml.DummyBo</value></property>
	</bean>

	<bean id="boSingleton" autowire="constructor" class="org.springframework.beans.factory.xml.DummyBoImpl"/>

</beans>
