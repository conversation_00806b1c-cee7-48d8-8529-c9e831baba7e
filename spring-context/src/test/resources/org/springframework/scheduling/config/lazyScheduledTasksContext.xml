<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:task="http://www.springframework.org/schema/task"
		xsi:schemaLocation="
			http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/task https://www.springframework.org/schema/task/spring-task.xsd"
		default-lazy-init="true">

	<task:scheduled-tasks>
		<task:scheduled ref="myTask" method="doWork" fixed-rate="10"/>
	</task:scheduled-tasks>

	<bean id="myTask" class="org.springframework.scheduling.config.LazyScheduledTasksBeanDefinitionParserTests$Task"/>
</beans>
