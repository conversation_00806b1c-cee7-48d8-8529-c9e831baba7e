<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	   			https://www.springframework.org/schema/beans/spring-beans.xsd
				http://www.springframework.org/schema/task
				https://www.springframework.org/schema/task/spring-task.xsd">

	<task:scheduled-tasks scheduler="testScheduler">
		<task:scheduled ref="testBean" method="test" fixed-rate="1000"/>
		<task:scheduled ref="testBean" method="test" fixed-rate="2000"/>
		<task:scheduled ref="testBean" method="test" fixed-delay="3000"/>
		<task:scheduled ref="testBean" method="test" fixed-delay="3500" initial-delay="250"/>
		<task:scheduled ref="testBean" method="test" cron="*/4 * 9-17 * * MON-FRI"/>
		<task:scheduled ref="testBean" method="test" trigger="customTrigger"/>
		<task:scheduled ref="testBean" method="test" fixed-rate="4000" initial-delay="500"/>
	</task:scheduled-tasks>

	<task:scheduler id="testScheduler"/>

	<bean id="testBean" class="org.springframework.scheduling.config.ScheduledTasksBeanDefinitionParserTests$TestBean"/>

	<bean id="customTrigger" class="org.springframework.scheduling.config.ScheduledTasksBeanDefinitionParserTests$TestTrigger"/>

</beans>
