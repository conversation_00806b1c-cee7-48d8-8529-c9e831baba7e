# this must only be used for ApplicationContexts, some classes are only appropriate for application contexts

rod.(class)=org.springframework.beans.testfixture.beans.TestBean
rod.name=Rod
rod.age=31

roderick.(parent)=rod
roderick.name=<PERSON><PERSON>

kerry.(class)=org.springframework.beans.testfixture.beans.TestBean
kerry.name=<PERSON>
kerry.age=34
kerry.spouse(ref)=rod

kathy.(class)=org.springframework.beans.testfixture.beans.TestBean
kathy.(singleton)=false

typeMismatch.(class)=org.springframework.beans.testfixture.beans.TestBean
typeMismatch.name=typeMismatch
typeMismatch.age=34x
typeMismatch.spouse(ref)=rod
typeMismatch.(singleton)=false

validEmpty.(class)=org.springframework.beans.testfixture.beans.TestBean

listenerVeto.(class)=org.springframework.beans.testfixture.beans.TestBean

typeMismatch.name=typeMismatch
typeMismatch.age=34x
typeMismatch.spouse(ref)=rod

singletonFactory.(class)=org.springframework.beans.testfixture.beans.factory.DummyFactory
singletonFactory.singleton=true

prototypeFactory.(class)=org.springframework.beans.testfixture.beans.factory.DummyFactory
prototypeFactory.singleton=false

mustBeInitialized.(class)=org.springframework.beans.testfixture.beans.MustBeInitialized

lifecycle.(class)=org.springframework.context.LifecycleContextBean

lifecyclePostProcessor.(class)=org.springframework.beans.testfixture.beans.LifecycleBean$PostProcessor
