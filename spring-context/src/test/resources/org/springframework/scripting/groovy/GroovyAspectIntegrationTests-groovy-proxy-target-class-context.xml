<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:lang="http://www.springframework.org/schema/lang"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.5.xsd
						http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.5.xsd
						http://www.springframework.org/schema/lang https://www.springframework.org/schema/lang/spring-lang-3.1.xsd">

    <aop:config >
       <aop:advisor id="logUserAdvisor" pointcut="@within(org.springframework.scripting.groovy.Log)" advice-ref="logUserAdvice"/>
    </aop:config>

    <bean id="logUserAdvice" class="org.springframework.scripting.groovy.LogUserAdvice" />

    <lang:groovy id="groovyBean" script-source="classpath:/org/springframework/scripting/groovy/GroovyServiceImpl.grv" refresh-check-delay="1000" proxy-target-class="true"/>

</beans>