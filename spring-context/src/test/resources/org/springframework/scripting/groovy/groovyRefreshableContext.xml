<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN 2.0//EN" "https://www.springframework.org/dtd/spring-beans-2.0.dtd">

<beans default-lazy-init="true">

	<bean class="org.springframework.scripting.support.ScriptFactoryPostProcessor">
		<property name="defaultRefreshCheckDelay" value="1"/>
	</bean>

	<bean id="calculator" class="org.springframework.scripting.groovy.GroovyScriptFactory">
		<constructor-arg value="classpath:org/springframework/scripting/groovy/Calculator.groovy"/>
	</bean>

	<bean id="messenger" class="org.springframework.scripting.groovy.GroovyScriptFactory">
		<constructor-arg value="classpath:org/springframework/scripting/groovy/Messenger.groovy"/>
		<property name="message" value="Hello World!"/>
	</bean>

	<bean id="messengerPrototype" class="org.springframework.scripting.groovy.GroovyScriptFactory" scope="prototype">
		<constructor-arg value="classpath:org/springframework/scripting/groovy/Messenger.groovy"/>
		<property name="message" value="Hello World!"/>
	</bean>

</beans>
