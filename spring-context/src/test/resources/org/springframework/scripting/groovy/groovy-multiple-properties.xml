<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:lang="http://www.springframework.org/schema/lang"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/lang https://www.springframework.org/schema/lang/spring-lang-2.5.xsd">

	<lang:groovy id="bean" script-source="classpath:org/springframework/scripting/groovy/ScriptBean.groovy" autowire="byType">
		<lang:property name="name" value="<PERSON>"/>
		<lang:property name="age" value="31"/>
	</lang:groovy>

	<lang:groovy id="bean2" script-source="classpath:org/springframework/scripting/groovy/ScriptBean.groovy" autowire="byName">
		<lang:property name="name" value="<PERSON>"/>
		<lang:property name="age" value="31"/>
	</lang:groovy>

	<lang:groovy id="bean3" script-source="classpath:org/springframework/scripting/groovy/ScriptBean.groovy" scope="prototype">
		<lang:property name="name" value="Sophie Marceau"/>
		<lang:property name="age" value="31"/>
	</lang:groovy>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

</beans>
