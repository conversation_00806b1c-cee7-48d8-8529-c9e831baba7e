<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN 2.0//EN" "https://www.springframework.org/dtd/spring-beans-2.0.dtd">

<beans>

	<bean class="org.springframework.jmx.export.MBeanExporter" autowire="byName">
		<property name="beans">
			<map>
				<entry key="bean:name=testBean4">
					<ref bean="testBean"/>
				</entry>
			</map>
		</property>
		<property name="assembler">
			<bean class="org.springframework.jmx.export.assembler.InterfaceBasedMBeanInfoAssembler">
				<property name="interfaceMappings">
					<props>
						<prop key="bean:name=testBean4">
 							org.springframework.jmx.export.assembler.IAdditionalTestMethods,
							org.springframework.jmx.export.assembler.ICustomJmxBean
 						</prop>
					</props>
				</property>
				<property name="notificationInfos">
					<bean class="org.springframework.jmx.export.metadata.ManagedNotification">
						<property name="name" value="My Notification"/>
						<property name="description" value="A Notification"/>
						<property name="notificationTypes" value="type.foo,type.bar"/>
					</bean>
				</property>
			</bean>
		</property>
	</bean>

	<bean id="testBean" class="org.springframework.jmx.JmxTestBean">
		<property name="name">
			<value>TEST</value>
		</property>
		<property name="age">
			<value>100</value>
		</property>
	</bean>

</beans
>