<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context https://www.springframework.org/schema/context/spring-context-2.5.xsd">

	<context:mbean-export server="server" registration="replaceExisting"/>

    <context:property-placeholder/>

    <bean id="server" class="org.springframework.jmx.support.MBeanServerFactoryBean"/>

	<bean name="testBean4" class="org.springframework.jmx.export.annotation.AnnotationTestBean" lazy-init="true">
		<property name="name" value="TEST"/>
		<property name="age" value="100"/>
	</bean>

	<bean name="testBean5" class="org.springframework.jmx.export.annotation.AnnotationTestBeanFactory"/>

	<bean name="spring:mbean=true" class="org.springframework.jmx.export.TestDynamicMBean" lazy-init="true"/>

	<bean name="spring:mbean=another" class="org.springframework.context.testfixture.jmx.export.Person" lazy-init="true">
		<property name="name" value="Juergen Hoeller"/>
	</bean>

	<bean id="notLoadable" class="does.not.exist" lazy-init="true"/>

</beans>
