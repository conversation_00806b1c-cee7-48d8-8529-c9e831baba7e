// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: sample.proto
// Protobuf Java Version: 4.27.0

package org.springframework.protobuf;

/**
 * Protobuf type {@code Msg}
 */
public final class Msg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Msg)
    MsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 27,
      /* patch= */ 0,
      /* suffix= */ "",
      Msg.class.getName());
  }
  // Use Msg.newBuilder() to construct.
  private Msg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Msg() {
    foo_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return org.springframework.protobuf.OuterSample.internal_static_Msg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return org.springframework.protobuf.OuterSample.internal_static_Msg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            org.springframework.protobuf.Msg.class, org.springframework.protobuf.Msg.Builder.class);
  }

  private int bitField0_;
  public static final int FOO_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object foo_ = "";
  /**
   * <code>optional string foo = 1;</code>
   * @return Whether the foo field is set.
   */
  @java.lang.Override
  public boolean hasFoo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string foo = 1;</code>
   * @return The foo.
   */
  @java.lang.Override
  public java.lang.String getFoo() {
    java.lang.Object ref = foo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        foo_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string foo = 1;</code>
   * @return The bytes for foo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFooBytes() {
    java.lang.Object ref = foo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      foo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BLAH_FIELD_NUMBER = 2;
  private org.springframework.protobuf.SecondMsg blah_;
  /**
   * <code>optional .SecondMsg blah = 2;</code>
   * @return Whether the blah field is set.
   */
  @java.lang.Override
  public boolean hasBlah() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .SecondMsg blah = 2;</code>
   * @return The blah.
   */
  @java.lang.Override
  public org.springframework.protobuf.SecondMsg getBlah() {
    return blah_ == null ? org.springframework.protobuf.SecondMsg.getDefaultInstance() : blah_;
  }
  /**
   * <code>optional .SecondMsg blah = 2;</code>
   */
  @java.lang.Override
  public org.springframework.protobuf.SecondMsgOrBuilder getBlahOrBuilder() {
    return blah_ == null ? org.springframework.protobuf.SecondMsg.getDefaultInstance() : blah_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, foo_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getBlah());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, foo_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getBlah());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof org.springframework.protobuf.Msg)) {
      return super.equals(obj);
    }
    org.springframework.protobuf.Msg other = (org.springframework.protobuf.Msg) obj;

    if (hasFoo() != other.hasFoo()) return false;
    if (hasFoo()) {
      if (!getFoo()
          .equals(other.getFoo())) return false;
    }
    if (hasBlah() != other.hasBlah()) return false;
    if (hasBlah()) {
      if (!getBlah()
          .equals(other.getBlah())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasFoo()) {
      hash = (37 * hash) + FOO_FIELD_NUMBER;
      hash = (53 * hash) + getFoo().hashCode();
    }
    if (hasBlah()) {
      hash = (37 * hash) + BLAH_FIELD_NUMBER;
      hash = (53 * hash) + getBlah().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static org.springframework.protobuf.Msg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.protobuf.Msg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static org.springframework.protobuf.Msg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static org.springframework.protobuf.Msg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static org.springframework.protobuf.Msg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static org.springframework.protobuf.Msg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(org.springframework.protobuf.Msg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Msg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Msg)
      org.springframework.protobuf.MsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.springframework.protobuf.OuterSample.internal_static_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.springframework.protobuf.OuterSample.internal_static_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.springframework.protobuf.Msg.class, org.springframework.protobuf.Msg.Builder.class);
    }

    // Construct using org.springframework.protobuf.Msg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getBlahFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      foo_ = "";
      blah_ = null;
      if (blahBuilder_ != null) {
        blahBuilder_.dispose();
        blahBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return org.springframework.protobuf.OuterSample.internal_static_Msg_descriptor;
    }

    @java.lang.Override
    public org.springframework.protobuf.Msg getDefaultInstanceForType() {
      return org.springframework.protobuf.Msg.getDefaultInstance();
    }

    @java.lang.Override
    public org.springframework.protobuf.Msg build() {
      org.springframework.protobuf.Msg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public org.springframework.protobuf.Msg buildPartial() {
      org.springframework.protobuf.Msg result = new org.springframework.protobuf.Msg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(org.springframework.protobuf.Msg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.foo_ = foo_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.blah_ = blahBuilder_ == null
            ? blah_
            : blahBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof org.springframework.protobuf.Msg) {
        return mergeFrom((org.springframework.protobuf.Msg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(org.springframework.protobuf.Msg other) {
      if (other == org.springframework.protobuf.Msg.getDefaultInstance()) return this;
      if (other.hasFoo()) {
        foo_ = other.foo_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasBlah()) {
        mergeBlah(other.getBlah());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              foo_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getBlahFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object foo_ = "";
    /**
     * <code>optional string foo = 1;</code>
     * @return Whether the foo field is set.
     */
    public boolean hasFoo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string foo = 1;</code>
     * @return The foo.
     */
    public java.lang.String getFoo() {
      java.lang.Object ref = foo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          foo_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string foo = 1;</code>
     * @return The bytes for foo.
     */
    public com.google.protobuf.ByteString
        getFooBytes() {
      java.lang.Object ref = foo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        foo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string foo = 1;</code>
     * @param value The foo to set.
     * @return This builder for chaining.
     */
    public Builder setFoo(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      foo_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string foo = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearFoo() {
      foo_ = getDefaultInstance().getFoo();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string foo = 1;</code>
     * @param value The bytes for foo to set.
     * @return This builder for chaining.
     */
    public Builder setFooBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      foo_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private org.springframework.protobuf.SecondMsg blah_;
    private com.google.protobuf.SingleFieldBuilder<
        org.springframework.protobuf.SecondMsg, org.springframework.protobuf.SecondMsg.Builder, org.springframework.protobuf.SecondMsgOrBuilder> blahBuilder_;
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     * @return Whether the blah field is set.
     */
    public boolean hasBlah() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     * @return The blah.
     */
    public org.springframework.protobuf.SecondMsg getBlah() {
      if (blahBuilder_ == null) {
        return blah_ == null ? org.springframework.protobuf.SecondMsg.getDefaultInstance() : blah_;
      } else {
        return blahBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public Builder setBlah(org.springframework.protobuf.SecondMsg value) {
      if (blahBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        blah_ = value;
      } else {
        blahBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public Builder setBlah(
        org.springframework.protobuf.SecondMsg.Builder builderForValue) {
      if (blahBuilder_ == null) {
        blah_ = builderForValue.build();
      } else {
        blahBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public Builder mergeBlah(org.springframework.protobuf.SecondMsg value) {
      if (blahBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          blah_ != null &&
          blah_ != org.springframework.protobuf.SecondMsg.getDefaultInstance()) {
          getBlahBuilder().mergeFrom(value);
        } else {
          blah_ = value;
        }
      } else {
        blahBuilder_.mergeFrom(value);
      }
      if (blah_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public Builder clearBlah() {
      bitField0_ = (bitField0_ & ~0x00000002);
      blah_ = null;
      if (blahBuilder_ != null) {
        blahBuilder_.dispose();
        blahBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public org.springframework.protobuf.SecondMsg.Builder getBlahBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return getBlahFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    public org.springframework.protobuf.SecondMsgOrBuilder getBlahOrBuilder() {
      if (blahBuilder_ != null) {
        return blahBuilder_.getMessageOrBuilder();
      } else {
        return blah_ == null ?
            org.springframework.protobuf.SecondMsg.getDefaultInstance() : blah_;
      }
    }
    /**
     * <code>optional .SecondMsg blah = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        org.springframework.protobuf.SecondMsg, org.springframework.protobuf.SecondMsg.Builder, org.springframework.protobuf.SecondMsgOrBuilder> 
        getBlahFieldBuilder() {
      if (blahBuilder_ == null) {
        blahBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            org.springframework.protobuf.SecondMsg, org.springframework.protobuf.SecondMsg.Builder, org.springframework.protobuf.SecondMsgOrBuilder>(
                getBlah(),
                getParentForChildren(),
                isClean());
        blah_ = null;
      }
      return blahBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:Msg)
  }

  // @@protoc_insertion_point(class_scope:Msg)
  private static final org.springframework.protobuf.Msg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new org.springframework.protobuf.Msg();
  }

  public static org.springframework.protobuf.Msg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Msg>
      PARSER = new com.google.protobuf.AbstractParser<Msg>() {
    @java.lang.Override
    public Msg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Msg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Msg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public org.springframework.protobuf.Msg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

