<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:aop="http://www.springframework.org/schema/aop"
		xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans-2.0.xsd
				http://www.springframework.org/schema/aop https://www.springframework.org/schema/aop/spring-aop-2.0.xsd">

	<aop:config>
		<aop:advisor advice-ref="advice" pointcut="execution(* *..ITestBean.*(..))"/>
	</aop:config>

	<bean id="advice" class="org.springframework.aop.interceptor.DebugInterceptor"/>

	<bean id="testBean" class="org.springframework.beans.testfixture.beans.TestBean"/>

	<bean id="singletonScoped" class="org.springframework.beans.testfixture.beans.TestBean">
		<aop:scoped-proxy/>
		<property name="name" value="Rob Harrop"/>
	</bean>

	<bean id="requestScoped" class="org.springframework.beans.testfixture.beans.TestBean" scope="request">
		<aop:scoped-proxy/>
		<property name="name" value="Rob Harrop"/>
	</bean>

	<bean id="sessionScoped" name="sessionScopedAlias" class="org.springframework.beans.testfixture.beans.TestBean" scope="session">
		<aop:scoped-proxy proxy-target-class="false"/>
		<property name="name" value="Rob Harrop"/>
	</bean>

</beans>
