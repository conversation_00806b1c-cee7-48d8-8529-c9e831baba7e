// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: sample.proto
// Protobuf Java Version: 4.27.0

package org.springframework.web.reactive.protobuf;

public interface SecondMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:SecondMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 blah = 1;</code>
   * @return Whether the blah field is set.
   */
  boolean hasBlah();
  /**
   * <code>optional int32 blah = 1;</code>
   * @return The blah.
   */
  int getBlah();
}
