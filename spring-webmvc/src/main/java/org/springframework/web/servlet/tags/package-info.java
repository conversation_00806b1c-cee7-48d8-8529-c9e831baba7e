/**
 * <p>This package contains Spring's JSP standard tag library for JSP 2.0+.
 * Supports JSP view implementations within Spring's Web MVC framework.
 * For more details on each tag, see the list of tags below with links to
 * individual tag classes, or check the {@code spring.tld} file:
 *
 * <ul>
 * <li>{@link org.springframework.web.servlet.tags.ArgumentTag The argument tag}
 * <li>{@link org.springframework.web.servlet.tags.BindTag The bind tag}
 * <li>{@link org.springframework.web.servlet.tags.BindErrorsTag The hasBindErrors tag}
 * <li>{@link org.springframework.web.servlet.tags.EscapeBodyTag The escapeBody tag}
 * <li>{@link org.springframework.web.servlet.tags.EvalTag The eval tag}
 * <li>{@link org.springframework.web.servlet.tags.HtmlEscapeTag The htmlEscape tag}
 * <li>{@link org.springframework.web.servlet.tags.MessageTag The message tag}
 * <li>{@link org.springframework.web.servlet.tags.NestedPathTag The nestedPath tag}
 * <li>{@link org.springframework.web.servlet.tags.ParamTag The param tag}
 * <li>{@link org.springframework.web.servlet.tags.ThemeTag The theme tag}
 * <li>{@link org.springframework.web.servlet.tags.TransformTag The transform tag}
 * <li>{@link org.springframework.web.servlet.tags.UrlTag The url tag}
 * </ul>
 *
 * <p>Please note that the various tags generated by this form tag library are
 * compliant with https://www.w3.org/TR/xhtml1/ and attendant
 * https://www.w3.org/TR/xhtml1/dtds.html#a_dtd_XHTML-1.0-Strict.
 */
@NonNullApi
@NonNullFields
package org.springframework.web.servlet.tags;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
