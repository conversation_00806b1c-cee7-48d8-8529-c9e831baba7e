description = "Spring Web MVC"

apply plugin: "kotlin"
apply plugin: "kotlinx-serialization"

dependencies {
	api(project(":spring-aop"))
	api(project(":spring-beans"))
	api(project(":spring-context"))
	api(project(":spring-core"))
	api(project(":spring-expression"))
	api(project(":spring-web"))
	compileOnly("jakarta.servlet:jakarta.servlet-api")
	compileOnly("com.google.code.findbugs:findbugs") {  // for groovy-templates
		exclude group: "dom4j", module: "dom4j"
	}
	optional(project(":spring-context-support"))  // for FreeMarker support
	optional(project(":spring-oxm"))
	optional("com.fasterxml.jackson.core:jackson-databind")
	optional("com.fasterxml.jackson.dataformat:jackson-dataformat-cbor")
	optional("com.fasterxml.jackson.dataformat:jackson-dataformat-smile")
	optional("com.fasterxml.jackson.dataformat:jackson-dataformat-xml")
	optional("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml")
	optional("com.github.librepdf:openpdf")
	optional("com.rometools:rome")
	optional("io.micrometer:context-propagation")
	optional("jakarta.el:jakarta.el-api")
	optional("jakarta.servlet.jsp:jakarta.servlet.jsp-api")
	optional("jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api")
	optional("jakarta.validation:jakarta.validation-api")
	optional("jakarta.xml.bind:jakarta.xml.bind-api")
	optional("org.apache.groovy:groovy-templates")
	optional("org.apache.poi:poi-ooxml")
	optional("org.freemarker:freemarker")
	optional("org.jetbrains.kotlin:kotlin-reflect")
	optional("org.jetbrains.kotlin:kotlin-stdlib")
	optional("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
	optional("org.jetbrains.kotlinx:kotlinx-serialization-cbor")
	optional("org.jetbrains.kotlinx:kotlinx-serialization-json")
	optional("org.jetbrains.kotlinx:kotlinx-serialization-protobuf")
	optional("org.reactivestreams:reactive-streams")
	optional("org.webjars:webjars-locator-core")
	optional("org.webjars:webjars-locator-lite")
	testCompileOnly("com.google.code.findbugs:findbugs") {  // for groovy-templates
		exclude group: "dom4j", module: "dom4j"
	}
	testImplementation(testFixtures(project(":spring-beans")))
	testImplementation(testFixtures(project(":spring-context")))
	testImplementation(testFixtures(project(":spring-core")))
	testImplementation(testFixtures(project(":spring-web")))
	testImplementation("commons-io:commons-io")
	testImplementation("io.projectreactor:reactor-core")
	testImplementation("io.reactivex.rxjava3:rxjava")
	testImplementation("jakarta.servlet:jakarta.servlet-api")
	testImplementation("jakarta.validation:jakarta.validation-api")
	testImplementation("jaxen:jaxen")
	testImplementation("org.dom4j:dom4j") {
		exclude group: "javax.xml.bind", module: "jaxb-api"
		exclude group: "jaxen", module: "jaxen"
		exclude group: "net.java.dev.msv", module: "xsdlib"
		exclude group: "pull-parser", module: "pull-parser"
		exclude group: "xpp3", module: "xpp3"
	}
	testImplementation("org.eclipse.jetty.ee10:jetty-ee10-servlet") {
		exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
	}
	testImplementation("org.hibernate:hibernate-validator")
	testImplementation("org.jetbrains.kotlin:kotlin-script-runtime")
	testImplementation("org.mozilla:rhino")
	testImplementation("org.skyscreamer:jsonassert")
	testImplementation("org.xmlunit:xmlunit-assertj")
	testImplementation("org.xmlunit:xmlunit-matchers")
	testRuntimeOnly("com.sun.activation:jakarta.activation")
	testRuntimeOnly("com.sun.xml.bind:jaxb-core")
	testRuntimeOnly("com.sun.xml.bind:jaxb-impl")
	testRuntimeOnly("org.apache.httpcomponents.client5:httpclient5")
	testRuntimeOnly("org.glassfish:jakarta.el")
	testRuntimeOnly("org.jetbrains.kotlin:kotlin-scripting-jsr223")
	testRuntimeOnly("org.jruby:jruby")
	testRuntimeOnly("org.python:jython-standalone")
	testRuntimeOnly("org.webjars:underscorejs")
}
