/*
 * Copyright 2002-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.docs.testing.mockmvc.assertj.mockmvctesterrequests

import org.assertj.core.api.Assertions.assertThat
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.assertj.MockMvcTester

class HotelControllerTests {

	private val mockMvc = MockMvcTester.of(HotelController())

	fun createHotel() {
		// tag::post[]
		assertThat(mockMvc.post().uri("/hotels/{id}", 42).accept(MediaType.APPLICATION_JSON))
			. // ...
			// end::post[]
			hasStatusOk()
	}

	fun createHotelMultipleAssertions() {
		// tag::post-exchange[]
		val result = mockMvc.post().uri("/hotels/{id}", 42)
			.accept(MediaType.APPLICATION_JSON).exchange()
		assertThat(result)
			. // ...
			// end::post-exchange[]
			hasStatusOk()
	}

	fun queryParameters() {
		// tag::query-parameters[]
		assertThat(mockMvc.get().uri("/hotels?thing={thing}", "somewhere"))
			. // ...
			//end::query-parameters[]
			hasStatusOk()
	}

	fun parameters() {
		// tag::parameters[]
		assertThat(mockMvc.get().uri("/hotels").param("thing", "somewhere"))
			. // ...
			// end::parameters[]
			hasStatusOk()
	}

	class HotelController
}