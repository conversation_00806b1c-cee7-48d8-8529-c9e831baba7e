[[expressions-example-classes]]
= Classes Used in the Examples

This section lists the classes used in the examples throughout this chapter.

== `Inventor`

[tabs]
======
Java::
+
[source,java,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor;

	import java.util.Date;
	import java.util.GregorianCalendar;

	public class Inventor {

		private String name;
		private String nationality;
		private String[] inventions;
		private Date birthdate;
		private PlaceOfBirth placeOfBirth;

		public Inventor(String name, String nationality) {
			GregorianCalendar c= new GregorianCalendar();
			this.name = name;
			this.nationality = nationality;
			this.birthdate = c.getTime();
		}

		public Inventor(String name, Date birthdate, String nationality) {
			this.name = name;
			this.nationality = nationality;
			this.birthdate = birthdate;
		}

		public Inventor() {
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getNationality() {
			return nationality;
		}

		public void setNationality(String nationality) {
			this.nationality = nationality;
		}

		public Date getBirthdate() {
			return birthdate;
		}

		public void setBirthdate(Date birthdate) {
			this.birthdate = birthdate;
		}

		public PlaceOfBirth getPlaceOfBirth() {
			return placeOfBirth;
		}

		public void setPlaceOfBirth(PlaceOfBirth placeOfBirth) {
			this.placeOfBirth = placeOfBirth;
		}

		public void setInventions(String[] inventions) {
			this.inventions = inventions;
		}

		public String[] getInventions() {
			return inventions;
		}
	}
----

Kotlin::
+
[source,kotlin,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor

	class Inventor(
		var name: String,
		var nationality: String,
		var inventions: Array<String>? = null,
		var birthdate: Date =  GregorianCalendar().time,
		var placeOfBirth: PlaceOfBirth? = null)
----
======

== `PlaceOfBirth`

[tabs]
======
Java::
+
[source,java,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor;

	public class PlaceOfBirth {

		private String city;
		private String country;

		public PlaceOfBirth(String city) {
			this.city=city;
		}

		public PlaceOfBirth(String city, String country) {
			this(city);
			this.country = country;
		}

		public String getCity() {
			return city;
		}

		public void setCity(String s) {
			this.city = s;
		}

		public String getCountry() {
			return country;
		}

		public void setCountry(String country) {
			this.country = country;
		}
	}
----

Kotlin::
+
[source,kotlin,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor

	class PlaceOfBirth(var city: String, var country: String? = null) {
----
======

== `Society`

[tabs]
======
Java::
+
[source,java,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor;

	import java.util.*;

	public class Society {

		private String name;

		public static String Advisors = "advisors";
		public static String President = "president";

		private List<Inventor> members = new ArrayList<>();
		private Map officers = new HashMap();

		public List getMembers() {
			return members;
		}

		public Map getOfficers() {
			return officers;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public boolean isMember(String name) {
			for (Inventor inventor : members) {
				if (inventor.getName().equals(name)) {
					return true;
				}
			}
			return false;
		}
	}
----

Kotlin::
+
[source,kotlin,indent=0,subs="verbatim,quotes",chomp="-packages"]
----
	package org.spring.samples.spel.inventor

	import java.util.*

	class Society {

		val Advisors = "advisors"
		val President = "president"

		var name: String? = null

		val members = ArrayList<Inventor>()
		val officers = mapOf<Any, Any>()

		fun isMember(name: String): Boolean {
			for (inventor in members) {
				if (inventor.name == name) {
					return true
				}
			}
			return false
		}
	}
----
======
