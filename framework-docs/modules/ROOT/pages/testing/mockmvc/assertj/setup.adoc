[[mockmvc-tester-setup]]
= Configuring MockMvcTester

`MockMvcTester` can be setup in one of two ways. One is to point directly to the
controllers you want to test and programmatically configure Spring MVC infrastructure.
The second is to point to Spring configuration with Spring MVC and controller
infrastructure in it.

TIP: For a comparison of those two modes, check xref:testing/mockmvc/setup-options.adoc[Setup Options].

To set up `MockMvcTester` for testing a specific controller, use the following:

include-code::./AccountControllerStandaloneTests[tag=snippet,indent=0]

To set up `MockMvcTester` through Spring configuration, use the following:

include-code::./AccountControllerIntegrationTests[tag=snippet,indent=0]

`MockMvcTester` can convert the JSON response body, or the result of a JSONPath expression,
to one of your domain object as long as the relevant `HttpMessageConverter` is registered.

If you use <PERSON> to serialize content to JSON, the following example registers the
converter:

include-code::./converter/AccountControllerIntegrationTests[tag=snippet,indent=0]

NOTE: The above assumes the converter has been registered as a Bean.

Finally, if you have a `MockMvc` instance handy, you can create a `MockMvcTester` by
providing the `MockMvc` instance to use using the `create` factory method.
