INSERT INTO users(first_name, last_name) VALUES('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>');

-- The following is not actually used; we just want to ensure that it does not
-- result in a parsing exception due to the nested single quote.
SELECT last_name AS "Juergen's Last Name" FROM users WHERE last_name='<PERSON><PERSON>';

INSERT INTO users(first_name, last_name) values('<PERSON>', '<PERSON><PERSON><PERSON>');