/*
 * Copyright 2002-2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.expression;

/**
 * Supported operations that an {@link OperatorOverloader} can implement for any pair of
 * operands.
 *
 * <AUTHOR>
 * @since 3.0
 */
public enum Operation {

	/**
	 * Add operation.
	 */
	ADD,

	/**
	 * Subtract operation.
	 */
	SUBTRACT,

	/**
	 * Divide operation.
	 */
	DIVIDE,

	/**
	 * Multiply operation.
	 */
	MULTIPLY,

	/**
	 * Modulus operation.
	 */
	MODULUS,

	/**
	 * Power operation.
	 */
	POWER

}
