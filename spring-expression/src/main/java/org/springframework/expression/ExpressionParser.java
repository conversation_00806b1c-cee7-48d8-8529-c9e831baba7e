/*
 * Copyright 2002-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.expression;

/**
 * Parses expression strings into compiled expressions that can be evaluated.
 *
 * <p>Supports parsing template expressions as well as standard expression strings.
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 3.0
 */
public interface ExpressionParser {

	/**
	 * Parse the expression string and return an {@link Expression} object that
	 * can be used for repeated evaluation.
	 * <p>Examples:
	 * <pre class="code">
	 *     3 + 4
	 *     name.firstName
	 * </pre>
	 * @param expressionString the raw expression string to parse
	 * @return an {@code Expression} for the parsed expression
	 * @throws ParseException if an exception occurred during parsing
	 */
	Expression parseExpression(String expressionString) throws ParseException;

	/**
	 * Parse the expression string and return an {@link Expression} object that
	 * can be used for repeated evaluation.
	 * <p>Examples:
	 * <pre class="code">
	 *     3 + 4
	 *     name.firstName
	 * </pre>
	 * @param expressionString the raw expression string to parse
	 * @param context a context for influencing the expression parsing routine
	 * @return an {@code Expression} for the parsed expression
	 * @throws ParseException if an exception occurred during parsing
	 */
	Expression parseExpression(String expressionString, ParserContext context) throws ParseException;

}
