/*
 * Copyright 2002-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.orm.jpa.hibernate.beans;

public class NoDefinitionInSpringContextTestBean extends TestBean {

	@SuppressWarnings("unused")
	private NoDefinitionInSpringContextTestBean() {
		throw new AssertionError("Unexpected call to the default constructor. " +
				"Is Spring trying to instantiate this class by itself, even though it should delegate to the fallback producer?"
		);
	}

	/*
	 * Expect instantiation through a non-default constructor, just to be sure that Spring will fail if it tries to instantiate it,
	 * and will subsequently delegate to the fallback bean instance producer.
 	 */
	public NoDefinitionInSpringContextTestBean(String name, BeanSource source) {
		setName(name);
		setSource(source);
	}

}
