<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:jms="http://www.springframework.org/schema/jms"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	   https://www.springframework.org/schema/beans/spring-beans.xsd
	   http://www.springframework.org/schema/jms
	   https://www.springframework.org/schema/jms/spring-jms-4.1.xsd">

	<jms:annotation-driven/>

	<bean class="org.springframework.jms.annotation.AbstractJmsAnnotationDrivenTests$JmsListenersBean"/>

	<bean id="jmsListenerContainerFactory"
		  class="org.springframework.jms.config.JmsListenerContainerTestFactory"/>


</beans>