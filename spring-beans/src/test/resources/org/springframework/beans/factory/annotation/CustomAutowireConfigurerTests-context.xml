<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
                https://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <bean id="resolver" class="org.springframework.beans.factory.annotation.CustomAutowireConfigurerTests$CustomResolver"/>

    <bean id="number-one" class="java.lang.String">
        <meta key="priority" value="1"/>
        <constructor-arg value="#1!"/>
    </bean>

    <bean id="one" class="java.lang.String" autowire-candidate="false">
        <meta key="priority" value="1"/>
        <constructor-arg value="#1"/>
    </bean>

    <bean id="number1" class="java.lang.String">
        <meta key="priority" value="1"/>
        <constructor-arg value="#1"/>
    </bean>

    <bean id="number-two" class="java.lang.String">
        <meta key="priority" value="2"/>
        <constructor-arg value="#2"/>
    </bean>

    <bean id="testBean"
          class="org.springframework.beans.factory.annotation.CustomAutowireConfigurerTests$TestBean"
          autowire="constructor"/>

</beans>