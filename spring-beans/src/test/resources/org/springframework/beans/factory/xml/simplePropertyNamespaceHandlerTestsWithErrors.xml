<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:p="http://www.springframework.org/schema/p"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="rob" class="org.springframework.beans.testfixture.beans.TestBean" p:name="<PERSON>" p:spouse-ref="sally">
		<property name="name" value="<PERSON>"/>
	</bean>

	<bean id="sally" class="org.springframework.beans.testfixture.beans.TestBean" p:name="<PERSON>" p:spouse-ref="rob"/>

</beans>
