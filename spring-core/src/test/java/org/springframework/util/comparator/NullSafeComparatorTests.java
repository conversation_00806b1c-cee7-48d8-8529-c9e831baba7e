/*
 * Copyright 2002-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.util.comparator;

import java.util.Comparator;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests for {@link NullSafeComparator}.
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
@Deprecated
class NullSafeComparatorTests {

	@Test
	@SuppressWarnings("unchecked")
	void shouldCompareWithNullsLow() {
		@SuppressWarnings("deprecation")
		Comparator<String> c = NullSafeComparator.NULLS_LOW;

		assertThat(c.compare("boo", "boo")).isZero();
		assertThat(c.compare(null, null)).isZero();
		assertThat(c.compare(null, "boo")).isNegative();
		assertThat(c.compare("boo", null)).isPositive();
	}

	@Test
	@SuppressWarnings("unchecked")
	void shouldCompareWithNullsHigh() {
		@SuppressWarnings("deprecation")
		Comparator<String> c = NullSafeComparator.NULLS_HIGH;

		assertThat(c.compare("boo", "boo")).isZero();
		assertThat(c.compare(null, null)).isZero();
		assertThat(c.compare(null, "boo")).isPositive();
		assertThat(c.compare("boo", null)).isNegative();
	}

}
