/*
 * Copyright 2002-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework;

import org.junit.platform.suite.api.IncludeClassNamePatterns;
import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;

/**
 * JUnit Platform based test suite for tests in the spring-core module.
 *
 * <p><strong>This suite is only intended to be used manually within an IDE.</strong>
 *
 * <AUTHOR>
 */
@Suite
@SelectPackages({
	"org.springframework.aot",
	"org.springframework.core",
	"org.springframework.util"
})
@IncludeClassNamePatterns(".*Tests?$")
class SpringCoreTestSuite {
}
