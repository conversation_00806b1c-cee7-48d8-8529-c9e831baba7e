description = "Spring WebSocket"

dependencies {
	api(project(":spring-context"))
	api(project(":spring-core"))
	api(project(":spring-web"))
	optional(project(":spring-messaging"))
	optional(project(":spring-webmvc"))
	optional("com.fasterxml.jackson.core:jackson-databind")
	optional("io.undertow:undertow-servlet")
	optional("io.undertow:undertow-websockets-jsr")
	optional("jakarta.servlet:jakarta.servlet-api")
	optional("jakarta.websocket:jakarta.websocket-api")
	optional("jakarta.websocket:jakarta.websocket-client-api")
	optional("org.apache.tomcat:tomcat-websocket") {
		exclude group: "org.apache.tomcat", module: "tomcat-servlet-api"
		exclude group: "org.apache.tomcat", module: "tomcat-websocket-api"
	}
	optional("org.eclipse.jetty.ee10.websocket:jetty-ee10-websocket-jakarta-server")
	optional("org.eclipse.jetty.ee10.websocket:jetty-ee10-websocket-jetty-server") {
		exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
	}
	optional("org.eclipse.jetty.ee10:jetty-ee10-webapp") {
		exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
	}
	optional("org.eclipse.jetty.websocket:jetty-websocket-jetty-api")
	optional("org.glassfish.tyrus:tyrus-container-servlet")
	testImplementation(testFixtures(project(":spring-core")))
	testImplementation(testFixtures(project(":spring-web")))
	testImplementation("io.projectreactor.netty:reactor-netty-http")
	testImplementation("org.apache.tomcat.embed:tomcat-embed-core")
	testImplementation("org.apache.tomcat.embed:tomcat-embed-websocket")
}
