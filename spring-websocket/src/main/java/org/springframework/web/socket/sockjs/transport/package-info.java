/**
 * Server-side support for SockJS transports including
 * {@link org.springframework.web.socket.sockjs.transport.TransportHandler} implementations
 * for processing incoming requests, their
 * {@link org.springframework.web.socket.sockjs.transport.session.AbstractSockJsSession session}
 * counterparts for sending messages over the various transports, and
 * {@link org.springframework.web.socket.sockjs.transport.handler.DefaultSockJsService}.
 */
@NonNullApi
@NonNullFields
package org.springframework.web.socket.sockjs.transport;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
